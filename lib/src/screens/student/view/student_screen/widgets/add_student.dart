import 'dart:developer';

import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/classes_screen.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:connectify_app/src/shared/widgets/select_contact/select_icon_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../generated/assets.dart';
import '../../../../../shared/services/media/controller/media_controller.dart';
import '../../../../../shared/widgets/shared_widgets.dart';
import '../../../../nursery/models/nursery_model_helper.dart';

class AddStudentDialog extends HookConsumerWidget {
  final bool fromClassDetails;
  final Widget navigateWidget;

  const AddStudentDialog(
      {super.key,
      this.fromClassDetails = false,
      this.navigateWidget = const ClassesScreen()});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return const AddSquareWidget(
      isStudent: true,
    ).onTap(() {
      showAddStudentDialog(context,
              navigateWidget: navigateWidget,
              fromClassDetails: fromClassDetails,
              ref: ref)
          .then((value) {
        mediaController.clearFiles();
      });
    });
  }
}

Future<void> showAddStudentDialog(
  BuildContext context, {
  required Widget navigateWidget,
  StudentModel? student,
  bool fromClassDetails = true,
  required WidgetRef ref,
}) async {
  final isEdit = student != null;

  final nurseryMaxStudents = NurseryModelHelper.currentNursery()?.maxStudents;

  log('Active_Students: ${StudentController.allActiveStudentsLength.value} Nursery_Max_Students: $nurseryMaxStudents');

  if (StudentController.allActiveStudentsLength.value >= nurseryMaxStudents! &&
      !isEdit) {
    QuickAlert.show(
      context: context,
      title: context.tr.warning,
      text: context.tr.maxStudentsReachedPleaseContactSupport,
      type: QuickAlertType.info,
      confirmBtnText: context.tr.contactSupport,
      cancelBtnText: context.tr.cancel,
      onConfirmBtnTap: () {
        launchUrl(
          Uri.parse('https://wa.me/201014878502'),
        );
      },
    );

    return;
  }

  showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final controllers = {
            ApiStrings.name: useTextEditingController(text: student?.name),
            ApiStrings.homeAddress:
                useTextEditingController(text: student?.homeAddress),
            ApiStrings.motherPhoneNumber:
                useTextEditingController(text: student?.motherPhoneNumber),
            ApiStrings.parentPhoneNumber:
                useTextEditingController(text: student?.parentPhoneNumber),
            ApiStrings.fees:
                useTextEditingController(text: student?.fees?.toString()),
          };

          final valueNotifiers = {
            ApiStrings.classString: useState<ClassModel?>(null),
            ApiStrings.birthDate: useState<DateTime?>(student?.birthDate),
            ApiStrings.subscriptionDate: useState<DateTime?>(student?.subscriptionDate),
            ApiStrings.gender: useState<String>(student?.gender ?? 'male'),
            ApiStrings.pickupPersons:
                useState<List<String>>(student?.pickupPersons ?? <String>[]),
          };

          final pickupControllers = useState<List<TextEditingController>>(
            student?.pickupPersons
                    .map((e) => useTextEditingController(text: e))
                    .toList() ??
                [],
          );

          final isParentPhoneAdded = useState(
              student?.parentPhoneNumber != null &&
                  (student?.parentPhoneNumber!.isNotEmpty ?? false));

          final formKey = useState(GlobalKey<FormState>());

          void clearData() {
            controllers[ApiStrings.name]!.clear();
            controllers[ApiStrings.homeAddress]!.clear();
            controllers[ApiStrings.motherPhoneNumber]!.clear();
            controllers[ApiStrings.parentPhoneNumber]!.clear();

            valueNotifiers[ApiStrings.classString]!.value = null;
            valueNotifiers[ApiStrings.birthDate]!.value = null;

            isParentPhoneAdded.value = false;

            ref.watch(mediaPickerControllerProvider).clearFiles();
          }

          final studentChangeNotifier =
              ref.watch(studentChangeNotifierProvider(context));

          final filePath = ref.watch(mediaPickerControllerProvider).filePath;

          final focusNode = useFocusNode();

          Future<void> addEditStudent() async {
            if (isEdit) {
              await studentChangeNotifier.editStudent(
                controllers: controllers,
                valueNotifiers: valueNotifiers,
                pickedImage: filePath,
                navigateWidget: navigateWidget,
                pickupControllers: pickupControllers.value,
                studentModel: student,
              );
            } else {
              final motherAndFatherUsers =
                  await studentChangeNotifier.motherAndFatherUsers(
                motherPhone: controllers[ApiStrings.motherPhoneNumber]!.text,
                parentPhone: controllers[ApiStrings.parentPhoneNumber]!.text,
              );

              final isMotherOrParentExist = motherAndFatherUsers.$1 != null ||
                  motherAndFatherUsers.$2 != null;

              if (isMotherOrParentExist) {
                QuickAlert.show(
                  title: context.tr.motherOrParentAlreadyRegistered,
                  text: context.tr
                      .IfYouContinueThisWillAffectParentsAppAndTheyCanSeeAllChildrenUnderTheirNumber,
                  context: context,
                  type: QuickAlertType.info,
                  confirmBtnText: context.tr.confirm,
                  showCancelBtn: true,
                  cancelBtnText: context.tr.cancel,
                  onConfirmBtnTap: () async {
                    context.back();

                    await studentChangeNotifier.addStudent(
                      controllers: controllers,
                      valueNotifiers: valueNotifiers,
                      pickupControllers: pickupControllers.value,
                      pickedImage: filePath,
                      navigateWidget: navigateWidget,
                      motherModel: motherAndFatherUsers.$1,
                      fatherModel: motherAndFatherUsers.$2,
                    );

                    if (context.mounted) {
                      clearData();
                    }
                  },
                );
                return;
              } else {
                await studentChangeNotifier.addStudent(
                  controllers: controllers,
                  valueNotifiers: valueNotifiers,
                  pickedImage: filePath,
                  navigateWidget: navigateWidget,
                  motherModel: motherAndFatherUsers.$1,
                  fatherModel: motherAndFatherUsers.$2,
                  pickupControllers: pickupControllers.value,
                );

                if (context.mounted) {
                  clearData();
                }
              }
            }
          }

          return PopScope(
            onPopInvoked: (didPop) =>
                ref.watch(mediaPickerControllerProvider).clearFiles(),
            child: AlertDialogWidget(
              networkImage: student?.image?.url ?? '',
              iconPath: Assets.iconsAdd,
              header: context.tr.addNewStudents,
              isLoading: studentChangeNotifier.isLoading,
              child: Form(
                key: formKey.value,
                child: Column(
                  children: [
                    //! Student Name
                    BaseTextField(
                      title: context.tr.studentName,
                      controller: controllers[ApiStrings.name],
                    ),

                    context.largeGap,

                    //? gender drop down
                    BaseDropDown(
                      icon: const Icon(Icons.person),
                      label: context.tr.gender,
                      selectedValue: valueNotifiers[ApiStrings.gender]!.value,
                      data: const ['male', 'female'],
                      onChanged: (value) {
                        valueNotifiers[ApiStrings.gender]!.value = value;
                      },
                    ),

                    context.largeGap,

                    //! Class Drop Down
                    ClassDropDown(
                      selectedClass: valueNotifiers[ApiStrings.classString]!
                          as ValueNotifier<ClassModel?>,
                      classId: student?.classModel?.id,
                    ),

                    context.largeGap,

                    //! Birth Date Field
                    BaseDatePicker(
                      selectedDateNotifier:
                          valueNotifiers[ApiStrings.birthDate]!
                              as ValueNotifier<DateTime?>,
                      label: context.tr.birthDate,
                    ),

                    context.largeGap,

                    //! Subscription Date Field
                    BaseDatePicker(
                      selectedDateNotifier:
                          valueNotifiers[ApiStrings.subscriptionDate]!
                              as ValueNotifier<DateTime?>,
                      label: 'Subscription Date',
                    ),

                    context.largeGap,

                    //! Mother Phone Number
                    BaseTextField(
                      suffixIcon: SelectContactIcon(
                        onSelected: (contact) {
                          controllers[ApiStrings.motherPhoneNumber]!.text =
                              normalizePhoneNumber(
                            contact.phoneNumbers?.firstOrNull,
                          );
                        },
                      ),
                      textInputType: TextInputType.phone,
                      title: context.tr.motherPhoneNumber,
                      controller: controllers[ApiStrings.motherPhoneNumber],
                    ),

                    context.smallGap,

                    //! Parent Phone Number
                    ValueListenableBuilder(
                        valueListenable: isParentPhoneAdded,
                        builder: (context, value, child) {
                          return !isParentPhoneAdded.value
                              ? TextButton(
                                  onPressed: () {
                                    isParentPhoneAdded.value = true;
                                  },
                                  child: Row(
                                    children: [
                                      const Icon(Icons.add).decorated(
                                        border: Border.all(
                                          color: ColorManager.primaryColor,
                                        ),
                                      ),
                                      context.smallGap,
                                      Text(context.tr.addParentsPhoneNumber),
                                    ],
                                  ))
                              : BaseTextField(
                                  suffixIcon: SelectContactIcon(
                                    onSelected: (contact) {
                                      controllers[ApiStrings.parentPhoneNumber]!
                                          .text = normalizePhoneNumber(
                                        contact.phoneNumbers?.firstOrNull,
                                      );
                                    },
                                  ),
                                  textInputType: TextInputType.phone,
                                  title: context.tr.parentPhoneNumber,
                                  controller:
                                      controllers[ApiStrings.parentPhoneNumber],
                                );
                        }),

                    context.largeGap,

                    //! Home Address
                    BaseTextField(
                      title: context.tr.homeAddress,
                      controller: controllers[ApiStrings.homeAddress],
                      maxLines: 4,
                    ),

                    context.largeGap,

                    Column(
                      children: [
                        ...pickupControllers.value.asMap().entries.map((entry) {
                          final index = entry.key;
                          final controller = entry.value;
                          final isLastItem =
                              index == pickupControllers.value.length - 1;

                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Row(
                              children: [
                                Expanded(
                                  child: BaseTextField(
                                    focusNode: isLastItem ? focusNode : null,
                                    controller: controller,
                                    isRequired: false,
                                    label: context.tr.pickupPerson,
                                    hint: context.tr.enterPickupPerson,
                                    onChanged: (value) {
                                      // No need to update a separate list since controllers hold the values
                                    },
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.remove_circle,
                                      color: Colors.red),
                                  onPressed: () {
                                    pickupControllers.value =
                                        List.from(pickupControllers.value)
                                          ..removeAt(index);
                                  },
                                ),
                              ],
                            ),
                          );
                        }),
                        TextButton(
                          onPressed: () {
                            pickupControllers.value = [
                              ...pickupControllers.value,
                              TextEditingController(text: ''),
                            ];

                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              focusNode.requestFocus();
                            });
                          },
                          child: Row(
                            children: [
                              const Icon(Icons.add).decorated(
                                border: Border.all(
                                  color: ColorManager.primaryColor,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(context.tr.addPickupPerson),
                            ],
                          ),
                        ),
                      ],
                    ),

                    context.largeGap,

                    //! Fees
                    BaseTextField(
                      title: context.tr.fees,
                      controller: controllers[ApiStrings.fees],
                    ),
                  ],
                ),
              ),
              onConfirm: () async {
                if (!formKey.value.currentState!.validate()) return;

                await addEditStudent();
              },
            ),
          );
        },
      );
    },
  );
}
