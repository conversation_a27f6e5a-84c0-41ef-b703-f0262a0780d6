import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/Activities/view/activiy/view/activities_screen,.dart';
import 'package:connectify_app/src/screens/activities/view/teacher_activty/teacher_activty_add_activity/view/teacher_activity_screen.dart';
import 'package:connectify_app/src/screens/attendance/view/attendance_screen.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/emergency/view/emergency_screen.dart';
import 'package:connectify_app/src/screens/events/view/events/events_screen.dart';
import 'package:connectify_app/src/screens/exam/view/exam_screen.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/students_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';

import '../../announcement/view/announcements_screen.dart';
import '../../class/view/classes_screen/classes_screen.dart';
import '../../financial/view/financial_screen.dart';
import '../../history/view/history_screen.dart';
import '../../meals/view/meals_screen.dart';
import '../../plan/view/plans_screen.dart';
import '../../student/view/pickup_screen/pickup_screen.dart';
import '../../supplies/view/admin_supplies/supplies_screen.dart';
import '../../teacher/view/taechers_screen/teachers_screen.dart';

class HomeModel {
  final String image;
  final String title;
  final String subTitle;
  final Widget widget;

  const HomeModel({
    required this.image,
    required this.widget,
    required this.title,
    required this.subTitle,
  });

  static List<HomeModel> homeList(BuildContext context) => [
        if (const UserModel().isAdmin)
          HomeModel(
              widget: const ClassesScreen(),
              image: Assets.svgClasses,
              title: context.tr.classes,
              subTitle: const UserModel().isAdmin ? context.tr.classes : '\n'),
        // if (const UserModel().isTeacher)
        //   HomeModel(
        //       widget: const TeacherClassScreen(),
        //       image: Assets.svgClasses,
        //       title: context.tr.myClass,
        //       subTitle: const UserModel().isAdmin ? context.tr.classes : '\n'),
        if (const UserModel().isAdmin)
          HomeModel(
              widget: const TeachersScreen(),
              image: Assets.svgStaff,
              title: context.tr.staff,
              subTitle: context.tr.members),
        HomeModel(
            widget: const StudentsScreen(),
            image: Assets.svgStudents,
            title: context.tr.students,
            subTitle: context.tr.students),
        HomeModel(
            widget: const ActivitiesScreen(),
            image: Assets.svgActivities,
            title: context.tr.activities,
            subTitle: context.tr.activities),
        if (!const UserModel().isAdmin)
          HomeModel(
              widget: const TeacherActivitiesScreen(),
              image: Assets.svgClasses,
              title: context.tr.sessions,
              subTitle: context.tr.sessions),
        HomeModel(
            widget: const EventsScreen(),
            image: Assets.svgEvents,
            title: context.tr.events,
            subTitle: context.tr.eventThisMonth),
        HomeModel(
            widget: const AttendanceScreen(),
            image: Assets.svgAttendance,
            title: context.tr.attendance,
            subTitle: '\n'),
        if (const UserModel().isAdmin)
          HomeModel(
              widget: const FinancialScreen(),
              image: Assets.svgFinancial,
              title: context.tr.financial,
              subTitle: '\n'),
        HomeModel(
            widget: const EmergencyScreen(),
            image: Assets.svgEmergency,
            title: context.tr.emergency,
            subTitle: '\n'),
        HomeModel(
            widget: const SuppliesScreen(),
            image: Assets.imagesSupplies,
            title: context.tr.supplies,
            subTitle: '\n'),
        HomeModel(
          widget: const ExamScreen(),
          image: Assets.svgExam,
          title: context.tr.exams,
          subTitle: '\n',
        ),
        if (const UserModel().isAdmin)
          HomeModel(
            widget: const MealsScreen(),
            image: Assets.svgMeals,
            title: context.tr.meals,
            subTitle: '\n',
          ),
        HomeModel(
          widget: const PickupScreen(),
          image: Assets.svgCar,
          title: context.tr.pickups,
          subTitle: '\n',
        ),
        // if (const UserModel().isAdmin)
        HomeModel(
          widget: const HistoryScreen(),
          image: Assets.svgHistory,
          title: context.tr.history,
          subTitle: '\n',
        ),
        HomeModel(
          widget: const AnnouncementsScreen(),
          image: Assets.svgAnnouncement,
          title: context.tr.announcements,
          subTitle: '\n',
        ),
        HomeModel(
          widget: const PlansScreen(),
          image: Assets.svgPlans,
          title: context.tr.plans,
          subTitle: '\n',
        ),
        if (const UserModel().isAdmin)
          HomeModel(
            widget: const HistoryScreen(),
            image: Assets.svgHistory,
            title: context.tr.history,
            subTitle: '\n',
          ),
      ];
}
