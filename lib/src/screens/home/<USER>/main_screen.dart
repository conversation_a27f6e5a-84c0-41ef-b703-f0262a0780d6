import 'package:connectify_app/src/screens/dashboard/view/dashboard_screen/dashboard_screen.dart';
import 'package:connectify_app/src/screens/financial/view/financial_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/bottom_nav_controller.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/home_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/floating_button.dart';
import 'package:connectify_app/src/screens/settings/view/settings_screen/settings_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/widgets/home_navigations/bottom_nav_bar_widget.dart';
import '../../financial/controllers/financial_tab_bar_controller.dart';
import '../../messages/view/view/messages_screen.dart';
import 'main_screen/widgets/main_app_bar.dart';

class MainScreen extends HookConsumerWidget {
  const MainScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final financialTabBarCtrl = ref.watch(financialTabBarController);

    void listenToNotifications(BuildContext context) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (WidgetsBinding.instance.lifecycleState !=
            AppLifecycleState.resumed) {
          FirebaseMessaging.instance.getInitialMessage().then((message) {
            if (message != null) {
              Log.w('ALL_DATA (initial): ${message.toMap()}');
              Log.w('NOTIFICATION (initial): ${message.notification?.toMap()}');
              Log.w(
                  'DATA (initial): ${message.data}\nTitle_Notification: ${message.notification?.title}');

              final isTitleContainsPaymentReceived =
                  message.notification?.title?.contains('Payment Received') ??
                      false;

              if (isTitleContainsPaymentReceived) {
                financialTabBarCtrl.changeIndex(2);

                context.to(const FinancialScreen(
                  currentIndex: 1,
                ));
              }
            }
          });
        }
      });

      FirebaseMessaging.onMessage.listen((message) {
        Log.i('onMessage: $message Data ${message.data}');
      });

      FirebaseMessaging.onMessageOpenedApp.listen((message) {
        Log.w('ALL_DATA: ${message.toMap()}');
        Log.w('NOTIFICATION: ${message.notification?.toMap()}');
        Log.w('DATA: ${message.data}');
        Log.w('Title_Notification_Data: ${message.notification?.title}');

        final isTitleContainsPaymentReceived =
            message.notification?.title?.contains('Payment Received') ?? false;

        if (isTitleContainsPaymentReceived) {
          financialTabBarCtrl.changeIndex(2);

          context.to(const FinancialScreen(
            currentIndex: 1,
          ));
        }
      });
    }

    useEffect(() {
      listenToNotifications(context);
      return () {};
    }, []);

    return SafeArea(
      top: false,
      child: WillPopScope(
        onWillPop: () async {
          // Navigator.pushAndRemoveUntil;
          return true;
        },
        child: Scaffold(
          appBar: MainAppBar(
            isHome: currentIndex == 0 ? true : false,
            title: selectedTitle(currentIndex, context),
          ),
          body: _SelectedScreen(
            currentIndex: currentIndex,
          ),
          bottomNavigationBar: const BottomNavBarWidget(),
          floatingActionButtonAnimator: FloatingActionButtonAnimator.scaling,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.miniCenterDocked,
          floatingActionButton: const FloatingButtonWidget(),
        ),
      ),
    );
  }
}

String selectedTitle(int currentIndex, BuildContext context) {
  // true : false;
  final bool isAfternoon = DateTime.now().hour >= 12;

  switch (currentIndex) {
    case 0:
      return isAfternoon ? context.tr.goodAfternoon : context.tr.goodMorning;
    case 1:
      return context.tr.dashboard;
    case 3:
      return context.tr.messages;
    case 4:
      return '';
  }

  return context.tr.goodMorning;
}

class _SelectedScreen extends StatelessWidget {
  final int currentIndex;

  const _SelectedScreen({
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    switch (currentIndex) {
      case 0:
        return const HomeScreen();
      case 1:
        return const DashboardScreen();
      case 3:
        return const MessagesScreen();
      case 4:
        return const SettingsScreen();
    }
    return const SizedBox.shrink();
  }
}
