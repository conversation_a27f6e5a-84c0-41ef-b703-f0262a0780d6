// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(date) =>
      "هل أنت متأكد من جعل هذا الاشتراك مدفوعاً لتاريخ ${date}؟";

  static String m1(name) => "إرسال رسالة جديدة إلى ${name}";

  static String m2(studentName) =>
      "اشتراك ${studentName} مستحق اليوم. يرجى تحصيل الدفعة.";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "IfYouContinueThisWillAffectParentsAppAndTheyCanSeeAllChildrenUnderTheirNumber":
        MessageLookupByLibrary.simpleMessage(
          "إذا واصلت، سيؤثر هذا على تطبيق الوالدين ويمكنهم رؤية جميع الأطفال تحت رقمهم",
        ),
    "Of": MessageLookupByLibrary.simpleMessage("من"),
    "SignupAsa": MessageLookupByLibrary.simpleMessage("التسجيل كـ"),
    "SkipForNow": MessageLookupByLibrary.simpleMessage("تخطي الآن"),
    "absent": MessageLookupByLibrary.simpleMessage("غائب"),
    "active": MessageLookupByLibrary.simpleMessage("نشط"),
    "activeStudents": MessageLookupByLibrary.simpleMessage("الطلاب النشطين"),
    "activities": MessageLookupByLibrary.simpleMessage("الأنشطة"),
    "activitiesCompleted": MessageLookupByLibrary.simpleMessage(
      "الأنشطة المكتملة",
    ),
    "activity": MessageLookupByLibrary.simpleMessage("النشاط"),
    "activityChart": MessageLookupByLibrary.simpleMessage("مخطط النشاط"),
    "activityDescription": MessageLookupByLibrary.simpleMessage("وصف النشاط"),
    "activityName": MessageLookupByLibrary.simpleMessage("اسم النشاط"),
    "add": MessageLookupByLibrary.simpleMessage("إضافة"),
    "addANewStaffMember": MessageLookupByLibrary.simpleMessage(
      "إضافة عضو فريق جديد",
    ),
    "addActivity": MessageLookupByLibrary.simpleMessage("إضافة نشاط"),
    "addAnnouncement": MessageLookupByLibrary.simpleMessage("إضافة إعلان"),
    "addImage": MessageLookupByLibrary.simpleMessage("إضافة صورة"),
    "addMedia": MessageLookupByLibrary.simpleMessage("إضافة صور"),
    "addNewBill": MessageLookupByLibrary.simpleMessage("إضافة فاتورة جديدة"),
    "addNewClass": MessageLookupByLibrary.simpleMessage("إضافة فصل جديد"),
    "addNewEvent": MessageLookupByLibrary.simpleMessage("إضافة حدث جديد"),
    "addNewInvoice": MessageLookupByLibrary.simpleMessage("إضافة ايراد جديدة"),
    "addNewQuestion": MessageLookupByLibrary.simpleMessage("إضافة سؤال جديد"),
    "addNewSection": MessageLookupByLibrary.simpleMessage("إضافة قسم جديد"),
    "addNewStudents": MessageLookupByLibrary.simpleMessage("إضافة طلاب جدد"),
    "addNote": MessageLookupByLibrary.simpleMessage("إضافة ملاحظة"),
    "addNurseryActivities": MessageLookupByLibrary.simpleMessage(
      "إضافة نشاط جديد",
    ),
    "addNurseryTeam": MessageLookupByLibrary.simpleMessage(
      "إضافة فريق الحضانة",
    ),
    "addNurseryTeamMember": MessageLookupByLibrary.simpleMessage(
      "إضافة عضو فريق جديد",
    ),
    "addParentsPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "إضافة رقم هاتف الوالدين",
    ),
    "addPickupPerson": MessageLookupByLibrary.simpleMessage(
      "إضافة شخص الاستلام",
    ),
    "addPlan": MessageLookupByLibrary.simpleMessage("إضافة خطة"),
    "addStudents": MessageLookupByLibrary.simpleMessage("إضافة الطلاب"),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تمت الإضافة بنجاح",
    ),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "adminAlreadyRegistered": MessageLookupByLibrary.simpleMessage(
      "المسؤول مسجل بالفعل",
    ),
    "adminSignUp": MessageLookupByLibrary.simpleMessage("تسجيل المسؤول"),
    "administrator": MessageLookupByLibrary.simpleMessage("مسؤول"),
    "all": MessageLookupByLibrary.simpleMessage("الكل"),
    "allStudents": MessageLookupByLibrary.simpleMessage("جميع الطلاب"),
    "alreadyHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "هل لديك حساب بالفعل؟",
    ),
    "amount": MessageLookupByLibrary.simpleMessage("الكمية"),
    "announcements": MessageLookupByLibrary.simpleMessage("الإعلانات"),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "areYouSureToDeleteThisActivity": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا النشاط؟",
    ),
    "areYouSureToDeleteThisBill": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه الفاتورة؟",
    ),
    "areYouSureToDeleteThisClass": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الفصل؟",
    ),
    "areYouSureToDeleteThisEvent": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الحدث؟",
    ),
    "areYouSureToDeleteThisInvoice": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا ايراد؟",
    ),
    "areYouSureToDeleteThisQuestion": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا السؤال؟",
    ),
    "areYouSureToDeleteThisStudent": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الطالب؟",
    ),
    "areYouSureToDeleteThisSupply": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه المستلزمات؟",
    ),
    "areYouSureToDeleteThisTeacher": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا المدرس؟",
    ),
    "areYouSureToDeleteYourAccount": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الحساب؟",
    ),
    "areYouSureToMakeThisSubscriptionPaid":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد من عمل هذا الاشتراك مدفوع؟",
        ),
    "areYouSureToMakeThisSubscriptionPaidFor": m0,
    "areYouSureToSendSubscriptionRemind": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من إرسال تذكير الاشتراك؟",
    ),
    "areYouSureYouWantToDeleteThisAnnouncement":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد من حذف هذا الإعلان؟",
        ),
    "areYouSureYouWantToDeleteThisPlan": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه الخطة؟",
    ),
    "assign": MessageLookupByLibrary.simpleMessage("تعيين"),
    "assignActivityToClass": MessageLookupByLibrary.simpleMessage(
      "تعيين النشاط للفصل",
    ),
    "assignSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "تعيين المستلزمات للطالب",
    ),
    "assignToClass": MessageLookupByLibrary.simpleMessage("تعيين إلى الفصل"),
    "assigned": MessageLookupByLibrary.simpleMessage("تم التعيين"),
    "attendance": MessageLookupByLibrary.simpleMessage("الحضور"),
    "attendanceChart": MessageLookupByLibrary.simpleMessage("مخطط الحضور"),
    "attendanceTracking": MessageLookupByLibrary.simpleMessage("تتبع الحضور"),
    "attended": MessageLookupByLibrary.simpleMessage("حضر"),
    "attendeesOfToday": MessageLookupByLibrary.simpleMessage("الحضور اليوم"),
    "back": MessageLookupByLibrary.simpleMessage("رجوع"),
    "billAmount": MessageLookupByLibrary.simpleMessage("مبلغ الفاتورة"),
    "billName": MessageLookupByLibrary.simpleMessage("اسم الفاتورة"),
    "bills": MessageLookupByLibrary.simpleMessage("الفواتير"),
    "billsChart": MessageLookupByLibrary.simpleMessage("مخطط الفواتير"),
    "birthDate": MessageLookupByLibrary.simpleMessage("تاريخ الميلاد"),
    "breakfast": MessageLookupByLibrary.simpleMessage("الإفطار"),
    "by": MessageLookupByLibrary.simpleMessage("بواسطة"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("تغيير اللغة"),
    "changePassword": MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
    "checkOurLatestJobApplicationsNow": MessageLookupByLibrary.simpleMessage(
      "تحقق من أحدث طلبات التوظيف لدينا الآن!",
    ),
    "chooseActivityAssignType": MessageLookupByLibrary.simpleMessage(
      "اختر نوع تعيين النشاط",
    ),
    "city": MessageLookupByLibrary.simpleMessage("المحافظة"),
    "classActivities": MessageLookupByLibrary.simpleMessage("أنشطة الفصل"),
    "classDescription": MessageLookupByLibrary.simpleMessage("وصف الفصل"),
    "className": MessageLookupByLibrary.simpleMessage("اسم الفصل"),
    "classes": MessageLookupByLibrary.simpleMessage("الفصول"),
    "clear": MessageLookupByLibrary.simpleMessage("مسح"),
    "clickToContact": MessageLookupByLibrary.simpleMessage("انقر للاتصال"),
    "completeVerification": MessageLookupByLibrary.simpleMessage(
      "إكمال التحقق",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "confirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور الجديدة",
    ),
    "confirmation": MessageLookupByLibrary.simpleMessage("التأكيد"),
    "congratulations": MessageLookupByLibrary.simpleMessage("تهانينا"),
    "contactSupport": MessageLookupByLibrary.simpleMessage("الاتصال بالدعم"),
    "createANewClass": MessageLookupByLibrary.simpleMessage("إنشاء فصل جديد"),
    "createANewSupply": MessageLookupByLibrary.simpleMessage(
      "إضافة مستلزمات جديدة",
    ),
    "createNewClass": MessageLookupByLibrary.simpleMessage("إنشاء فصل جديد"),
    "currentActivity": MessageLookupByLibrary.simpleMessage("النشاط الحالي"),
    "currentMonth": MessageLookupByLibrary.simpleMessage("الشهر الحالي"),
    "cv": MessageLookupByLibrary.simpleMessage("السيرة الذاتية"),
    "dailySchedule": MessageLookupByLibrary.simpleMessage("الجدول اليومي"),
    "dashboard": MessageLookupByLibrary.simpleMessage("لوحة التحكم"),
    "date": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "day": MessageLookupByLibrary.simpleMessage("اليوم"),
    "delete": MessageLookupByLibrary.simpleMessage("حذف"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("حذف الحساب"),
    "deleteAnnouncement": MessageLookupByLibrary.simpleMessage("حذف الإعلان"),
    "deletePlan": MessageLookupByLibrary.simpleMessage("حذف الخطة"),
    "deletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم الحذف بنجاح",
    ),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "didNotGetCode": MessageLookupByLibrary.simpleMessage(
      "ألم تحصل على الرمز؟",
    ),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "due": MessageLookupByLibrary.simpleMessage("مستحق"),
    "edit": MessageLookupByLibrary.simpleMessage("تحرير"),
    "editActivity": MessageLookupByLibrary.simpleMessage("تعديل النشاط"),
    "editAnnouncement": MessageLookupByLibrary.simpleMessage("تعديل الإعلان"),
    "editClass": MessageLookupByLibrary.simpleMessage("تحرير الفصل"),
    "editEvent": MessageLookupByLibrary.simpleMessage("تحرير الحدث"),
    "editPlan": MessageLookupByLibrary.simpleMessage("تعديل الخطة"),
    "editSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم التحرير بنجاح",
    ),
    "editTeacher": MessageLookupByLibrary.simpleMessage("تحرير المدرس"),
    "education": MessageLookupByLibrary.simpleMessage("التعليم"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "emergency": MessageLookupByLibrary.simpleMessage("الطوارئ"),
    "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterEtisalatCashNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم Etisalat Cash",
    ),
    "enterInstapayNumberOrLink": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم أو رابط InstaPay",
    ),
    "enterNewPassword": MessageLookupByLibrary.simpleMessage(
      "أدخل كلمة مرور جديدة",
    ),
    "enterOrangeCashNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم Orange Cash",
    ),
    "enterOtp": MessageLookupByLibrary.simpleMessage("أدخل رمز OTP"),
    "enterPhoneNumberFirst": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتفك أولاً",
    ),
    "enterPickupPerson": MessageLookupByLibrary.simpleMessage(
      "أدخل شخص الاستلام",
    ),
    "enterSectionDescription": MessageLookupByLibrary.simpleMessage(
      "أدخل وصف القسم",
    ),
    "enterSectionTitle": MessageLookupByLibrary.simpleMessage(
      "أدخل عنوان القسم",
    ),
    "enterValidNurseryName": MessageLookupByLibrary.simpleMessage(
      "أدخل اسم حضانة صحيح",
    ),
    "enterValidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتف صالح",
    ),
    "enterVodafoneCashNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم Vodafone Cash",
    ),
    "enterWeCashNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم WE Cash",
    ),
    "errorOccurred": MessageLookupByLibrary.simpleMessage("حدث خطأ"),
    "eventName": MessageLookupByLibrary.simpleMessage("اسم الحدث"),
    "eventThisMonth": MessageLookupByLibrary.simpleMessage("حدث هذا الشهر"),
    "eventType": MessageLookupByLibrary.simpleMessage("نوع الحدث"),
    "events": MessageLookupByLibrary.simpleMessage("الأحداث"),
    "exams": MessageLookupByLibrary.simpleMessage("الامتحانات"),
    "expectedSalary": MessageLookupByLibrary.simpleMessage("الراتب المتوقع"),
    "failedToUpdatePaymentMethods": MessageLookupByLibrary.simpleMessage(
      "فشل في تحديث طرق الدفع",
    ),
    "father": MessageLookupByLibrary.simpleMessage("الأب"),
    "fees": MessageLookupByLibrary.simpleMessage("الرسوم"),
    "financial": MessageLookupByLibrary.simpleMessage("المالية"),
    "finishLetsStart": MessageLookupByLibrary.simpleMessage("إنهاء، لنبدأ"),
    "food": MessageLookupByLibrary.simpleMessage("الطعام"),
    "forgetPassword": MessageLookupByLibrary.simpleMessage("نسيت كلمة المرور"),
    "friday": MessageLookupByLibrary.simpleMessage("الجمعة"),
    "from": MessageLookupByLibrary.simpleMessage("من"),
    "fromTimeShouldBeBeforeToTime": MessageLookupByLibrary.simpleMessage(
      "يجب أن يكون وقت البداية قبل وقت النهاية",
    ),
    "gender": MessageLookupByLibrary.simpleMessage("النوع"),
    "getTalkingFrom": MessageLookupByLibrary.simpleMessage(
      "كل يوم هو رحلة جديده\n سجل دخول للانضمام إلينا.",
    ),
    "goodAfternoon": MessageLookupByLibrary.simpleMessage("مساء الخير!"),
    "goodMorning": MessageLookupByLibrary.simpleMessage("صباح الخير!"),
    "haveAnyQuestionsContactUs": MessageLookupByLibrary.simpleMessage(
      "هل لديك أي أسئلة؟\nاتصل بنا",
    ),
    "history": MessageLookupByLibrary.simpleMessage("السجل"),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "homeAddress": MessageLookupByLibrary.simpleMessage("العنوان"),
    "iHaveReadThe": MessageLookupByLibrary.simpleMessage("لقد قرأت"),
    "inClothes": MessageLookupByLibrary.simpleMessage("الملابس"),
    "inTheDiaper": MessageLookupByLibrary.simpleMessage("الحفاض"),
    "inTheToilet": MessageLookupByLibrary.simpleMessage("الحمام"),
    "incomeChart": MessageLookupByLibrary.simpleMessage("مخطط الدخل"),
    "invoiceAmount": MessageLookupByLibrary.simpleMessage("مبلغ الايراد"),
    "invoiceName": MessageLookupByLibrary.simpleMessage("اسم الايراد"),
    "invoices": MessageLookupByLibrary.simpleMessage("الايرادات"),
    "invoicesChart": MessageLookupByLibrary.simpleMessage("مخطط الايرادات"),
    "jobApplication": MessageLookupByLibrary.simpleMessage("طلب التوظيف"),
    "jobApplicationDetails": MessageLookupByLibrary.simpleMessage(
      "تفاصيل طلب التوظيف",
    ),
    "jobApplications": MessageLookupByLibrary.simpleMessage("طلبات التوظيف"),
    "jobTitle": MessageLookupByLibrary.simpleMessage("الوظيفة"),
    "jobs": MessageLookupByLibrary.simpleMessage("الوظائف"),
    "letsDoAGreatJob": MessageLookupByLibrary.simpleMessage(
      "دعونا نقوم بعمل رائع",
    ),
    "letsStart": MessageLookupByLibrary.simpleMessage("لنبدأ"),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "lunch": MessageLookupByLibrary.simpleMessage("الغداء"),
    "matherPhoneNumber": MessageLookupByLibrary.simpleMessage("رقم هاتف الأم"),
    "maxStudentsReachedPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "تم الوصول إلى الحد الأقصى للطلاب، يرجى الاتصال بالدعم!",
        ),
    "maxUploadFileSizeIsOnly5MB": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى لحجم صورة التحميل هو 5 ميجابايت فقط",
    ),
    "maxUploadFilesIsOnly4": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى لصور التحميل هو 4 فقط",
    ),
    "mealAmount": MessageLookupByLibrary.simpleMessage("كمية الوجبة"),
    "mealType": MessageLookupByLibrary.simpleMessage("نوع الوجبة"),
    "meals": MessageLookupByLibrary.simpleMessage("الوجبات"),
    "media": MessageLookupByLibrary.simpleMessage("الصور"),
    "members": MessageLookupByLibrary.simpleMessage("الأعضاء"),
    "message": MessageLookupByLibrary.simpleMessage("الرسالة"),
    "messageSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال الرسالة بنجاح",
    ),
    "messages": MessageLookupByLibrary.simpleMessage("الرسائل"),
    "monday": MessageLookupByLibrary.simpleMessage("الاثنين"),
    "month": MessageLookupByLibrary.simpleMessage("الشهر"),
    "more": MessageLookupByLibrary.simpleMessage("أكثر"),
    "mother": MessageLookupByLibrary.simpleMessage("الأم"),
    "motherOrParentAlreadyRegistered": MessageLookupByLibrary.simpleMessage(
      "الأم أو الوالد مسجل بالفعل",
    ),
    "motherPhoneNumber": MessageLookupByLibrary.simpleMessage("رقم هاتف الأم"),
    "myClass": MessageLookupByLibrary.simpleMessage("فصلي"),
    "myClasses": MessageLookupByLibrary.simpleMessage("فصولي"),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "next": MessageLookupByLibrary.simpleMessage("التالي"),
    "noActivities": MessageLookupByLibrary.simpleMessage("لا توجد أنشطة"),
    "noActivitiesFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على أنشطة",
    ),
    "noBills": MessageLookupByLibrary.simpleMessage("لا توجد فواتير"),
    "noClasses": MessageLookupByLibrary.simpleMessage("لا توجد فصول"),
    "noData": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "noEvents": MessageLookupByLibrary.simpleMessage("لا توجد أحداث"),
    "noHistoryForThisDate": MessageLookupByLibrary.simpleMessage(
      "لا توجد سجلات لهذا التاريخ",
    ),
    "noInvoices": MessageLookupByLibrary.simpleMessage("لا توجد ايرادات"),
    "noJobApplications": MessageLookupByLibrary.simpleMessage(
      "لا توجد طلبات توظيف",
    ),
    "noMedia": MessageLookupByLibrary.simpleMessage("لا توجد صور"),
    "noNotifications": MessageLookupByLibrary.simpleMessage("لا توجد إشعارات"),
    "noPersons": MessageLookupByLibrary.simpleMessage("لا يوجد أشخاص"),
    "noPlansForThisMonth": MessageLookupByLibrary.simpleMessage(
      "لا توجد خطط لهذا الشهر",
    ),
    "noQuestions": MessageLookupByLibrary.simpleMessage("لا توجد أسئلة"),
    "noResultsFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على نتائج",
    ),
    "noStudents": MessageLookupByLibrary.simpleMessage("لا يوجد طلاب"),
    "noSubscriptionDateSet": MessageLookupByLibrary.simpleMessage(
      "لم يتم تحديد تاريخ الاشتراك",
    ),
    "noSupplies": MessageLookupByLibrary.simpleMessage("لا توجد مستلزمات"),
    "noTeachers": MessageLookupByLibrary.simpleMessage("لا يوجد مدرسون"),
    "none": MessageLookupByLibrary.simpleMessage("لا شيء"),
    "note": MessageLookupByLibrary.simpleMessage("الملاحظة"),
    "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
    "numberOfStudents": MessageLookupByLibrary.simpleMessage("عدد الطلاب"),
    "nurseryActivities": MessageLookupByLibrary.simpleMessage(
      "إضافة أنشطة الحضانة",
    ),
    "nurseryDataNotFound": MessageLookupByLibrary.simpleMessage(
      "بيانات الحضانة غير موجودة",
    ),
    "nurseryLogo": MessageLookupByLibrary.simpleMessage("شعار الحضانة"),
    "nurseryName": MessageLookupByLibrary.simpleMessage("اسم الحضانة"),
    "openCV": MessageLookupByLibrary.simpleMessage("فتح السيرة الذاتية"),
    "paid": MessageLookupByLibrary.simpleMessage("مدفوع"),
    "paidSuccessfully": MessageLookupByLibrary.simpleMessage("تم الدفع بنجاح"),
    "parentPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "رقم هاتف الوالدين",
    ),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "passwordConfirmation": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "كلمات المرور غير متطابقة",
    ),
    "passwordsShouldMatch": MessageLookupByLibrary.simpleMessage(
      "يجب أن تتطابق كلمات المرور",
    ),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("طريقة الدفع"),
    "paymentMethods": MessageLookupByLibrary.simpleMessage("طرق الدفع"),
    "paymentMethodsUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث طرق الدفع بنجاح",
    ),
    "paymentPendingApproval": MessageLookupByLibrary.simpleMessage(
      "الدفع في انتظار الموافقة",
    ),
    "paymentScreenshot": MessageLookupByLibrary.simpleMessage(
      "لقطة شاشة الدفع",
    ),
    "persons": MessageLookupByLibrary.simpleMessage("أشخاص"),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختيار صورة"),
    "pickupPerson": MessageLookupByLibrary.simpleMessage("شخص الاستلام"),
    "pickups": MessageLookupByLibrary.simpleMessage("الاستلام"),
    "plan": MessageLookupByLibrary.simpleMessage("خطة"),
    "plans": MessageLookupByLibrary.simpleMessage("الخطط"),
    "pleaseAcceptTerms": MessageLookupByLibrary.simpleMessage(
      "الرجاء قبول الشروط",
    ),
    "pleaseAddAtLeastOneSection": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة قسم واحد على الأقل",
    ),
    "pleaseAddTheStudentSubscriptionDateFirst":
        MessageLookupByLibrary.simpleMessage(
          "يرجى إضافة تاريخ اشتراك الطالب أولاً قبل الدفع.",
        ),
    "pleaseEnterAValidFromToTime": MessageLookupByLibrary.simpleMessage(
      "برجاء ادخال وقت من و إلى بشكل صحيح",
    ),
    "pleasePickAnImage": MessageLookupByLibrary.simpleMessage(
      "الرجاء اختيار صورة",
    ),
    "pleaseVerifyPhone": MessageLookupByLibrary.simpleMessage(
      "الرجاء التحقق من الهاتف",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
    "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
    "question": MessageLookupByLibrary.simpleMessage("سؤال"),
    "reminderSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال التذكير بنجاح",
    ),
    "removeImage": MessageLookupByLibrary.simpleMessage("إزالة الصورة"),
    "removeSection": MessageLookupByLibrary.simpleMessage("إزالة القسم"),
    "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
    "resend": MessageLookupByLibrary.simpleMessage("إعادة إرسال"),
    "resendCode": MessageLookupByLibrary.simpleMessage("إعادة إرسال الرمز"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "إعادة تعيين كلمة المرور",
    ),
    "results": MessageLookupByLibrary.simpleMessage("النتائج"),
    "saturday": MessageLookupByLibrary.simpleMessage("السبت"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "savePaymentMethods": MessageLookupByLibrary.simpleMessage("حفظ طرق الدفع"),
    "savedSuccessfully": MessageLookupByLibrary.simpleMessage("تم الحفظ بنجاح"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "searchClasses": MessageLookupByLibrary.simpleMessage("البحث عن الفصول"),
    "searchQuestion": MessageLookupByLibrary.simpleMessage("البحث عن سؤال"),
    "searchStudent": MessageLookupByLibrary.simpleMessage("البحث عن طالب"),
    "section": MessageLookupByLibrary.simpleMessage("القسم"),
    "sectionDescription": MessageLookupByLibrary.simpleMessage("وصف القسم"),
    "sectionImage": MessageLookupByLibrary.simpleMessage("صورة القسم"),
    "sectionTitle": MessageLookupByLibrary.simpleMessage("عنوان القسم"),
    "sections": MessageLookupByLibrary.simpleMessage("الأقسام"),
    "selectClass": MessageLookupByLibrary.simpleMessage("يرجى اختيار فصل"),
    "selectClasses": MessageLookupByLibrary.simpleMessage("اختر الفصول"),
    "selectPeriod": MessageLookupByLibrary.simpleMessage("اختر الفترة"),
    "selectTarget": MessageLookupByLibrary.simpleMessage("اختر الهدف"),
    "send": MessageLookupByLibrary.simpleMessage("إرسال"),
    "sendANewMessage": MessageLookupByLibrary.simpleMessage(
      "إرسال رسالة جديدة",
    ),
    "sendANewMessageTo": m1,
    "sendSupplies": MessageLookupByLibrary.simpleMessage("إرسال المستلزمات"),
    "sendSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "إرسال المستلزمات للطالب",
    ),
    "sentVerificationCode": MessageLookupByLibrary.simpleMessage(
      "لقد أرسلنا رمز تحقق إلى",
    ),
    "sessions": MessageLookupByLibrary.simpleMessage("الجدول اليومي"),
    "setupYourClasses": MessageLookupByLibrary.simpleMessage("إعداد فصولك"),
    "signIn": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "signUp": MessageLookupByLibrary.simpleMessage("اشتراك"),
    "singleActivity": MessageLookupByLibrary.simpleMessage("نشاط واحد"),
    "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
    "skipForNow": MessageLookupByLibrary.simpleMessage("تخطي الآن"),
    "sleep": MessageLookupByLibrary.simpleMessage("النوم"),
    "snack": MessageLookupByLibrary.simpleMessage("الوجبة الخفيفة"),
    "some": MessageLookupByLibrary.simpleMessage("بعض"),
    "speakWithConfidence": MessageLookupByLibrary.simpleMessage("تحدث بثقة"),
    "staff": MessageLookupByLibrary.simpleMessage("الموظفين"),
    "stool": MessageLookupByLibrary.simpleMessage("البراز"),
    "student": MessageLookupByLibrary.simpleMessage("الطالب"),
    "studentAndClass": MessageLookupByLibrary.simpleMessage("الطالب والفصل"),
    "studentName": MessageLookupByLibrary.simpleMessage("اسم الطالب"),
    "students": MessageLookupByLibrary.simpleMessage("الطلاب"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "subscriptionDate": MessageLookupByLibrary.simpleMessage("تاريخ الاشتراك"),
    "subscriptionDateNeeded": MessageLookupByLibrary.simpleMessage(
      "تاريخ الاشتراك مطلوب",
    ),
    "subscriptionDateRequired": MessageLookupByLibrary.simpleMessage(
      "تاريخ الاشتراك مطلوب",
    ),
    "subscriptionExpiredPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "انتهى الاشتراك، يرجى الاتصال بالدعم !",
        ),
    "subscriptionRemind": MessageLookupByLibrary.simpleMessage(
      "تذكير بالاشتراك",
    ),
    "subscriptionReminderBody": m2,
    "subscriptionReminderTitle": MessageLookupByLibrary.simpleMessage(
      "تذكير دفع الاشتراك",
    ),
    "subscriptions": MessageLookupByLibrary.simpleMessage("الاشتراكات"),
    "sunday": MessageLookupByLibrary.simpleMessage("الأحد"),
    "supplies": MessageLookupByLibrary.simpleMessage("المستلزمات"),
    "supply": MessageLookupByLibrary.simpleMessage("المستلزمات"),
    "supplyName": MessageLookupByLibrary.simpleMessage("اسم المستلزمات"),
    "tClass": MessageLookupByLibrary.simpleMessage("الفصل"),
    "target": MessageLookupByLibrary.simpleMessage("الهدف"),
    "teacher": MessageLookupByLibrary.simpleMessage("مدرس"),
    "teacherInfo": MessageLookupByLibrary.simpleMessage("معلومات المدرس"),
    "teacherName": MessageLookupByLibrary.simpleMessage("اسم المدرس"),
    "teacherSignUp": MessageLookupByLibrary.simpleMessage("تسجيل المدرس"),
    "teachers": MessageLookupByLibrary.simpleMessage("المدرسون"),
    "team": MessageLookupByLibrary.simpleMessage("الفريق"),
    "thisFieldIsRequired": MessageLookupByLibrary.simpleMessage(
      "هذا الحقل مطلوب",
    ),
    "thursday": MessageLookupByLibrary.simpleMessage("الخميس"),
    "title": MessageLookupByLibrary.simpleMessage("العنوان"),
    "to": MessageLookupByLibrary.simpleMessage("إلى"),
    "today": MessageLookupByLibrary.simpleMessage("اليوم"),
    "toilet": MessageLookupByLibrary.simpleMessage("الحمام"),
    "toiletType": MessageLookupByLibrary.simpleMessage("نوع الحمام"),
    "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
    "tuesday": MessageLookupByLibrary.simpleMessage("الثلاثاء"),
    "unAssign": MessageLookupByLibrary.simpleMessage("إلغاء التعيين"),
    "unableToCalculateNextPaymentDate": MessageLookupByLibrary.simpleMessage(
      "غير قادر على حساب تاريخ الدفع التالي.",
    ),
    "unpaid": MessageLookupByLibrary.simpleMessage("غير مدفوع"),
    "update": MessageLookupByLibrary.simpleMessage("تحديث"),
    "updateRequired": MessageLookupByLibrary.simpleMessage(
      "مطلوب تحديث للاستمرار في استخدام التطبيق. يرجى تحديثه الآن.",
    ),
    "uploadLogo": MessageLookupByLibrary.simpleMessage("تحميل الشعار"),
    "urine": MessageLookupByLibrary.simpleMessage("البول"),
    "userNotFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على المستخدم",
    ),
    "validateYourPhoneFirstPlease": MessageLookupByLibrary.simpleMessage(
      "الرجاء التحقق من هاتفك أولاً",
    ),
    "verificationCodeIsWrong": MessageLookupByLibrary.simpleMessage(
      "رمز التحقق خاطئ",
    ),
    "verificationSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم التحقق بنجاح",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("التحقق"),
    "warning": MessageLookupByLibrary.simpleMessage("تحذير"),
    "wednesday": MessageLookupByLibrary.simpleMessage("الأربعاء"),
    "weekly": MessageLookupByLibrary.simpleMessage("أسبوعياً"),
    "weeklyActivity": MessageLookupByLibrary.simpleMessage("نشاط أسبوعي"),
    "youAreParentPleaseLoginOnParentApplication":
        MessageLookupByLibrary.simpleMessage(
          "أنت ولي أمر، يرجى تسجيل الدخول على تطبيق الوالدين !",
        ),
    "youAreParentPleaseRegisterOnParentApplication":
        MessageLookupByLibrary.simpleMessage(
          "أنت ولي أمر، يرجى التسجيل على تطبيق الوالدين !",
        ),
    "youCannotDeleteThisQuestionBecauseitsHasStudentResults":
        MessageLookupByLibrary.simpleMessage(
          "لا يمكنك حذف هذا السؤال لأنه يحتوي على نتائج للطلاب",
        ),
  };
}
