// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(date) =>
      "Are you sure to make this subscription paid for ${date}?";

  static String m1(name) => "Send a new message to ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "IfYouContinueThisWillAffectParentsAppAndTheyCanSeeAllChildrenUnderTheirNumber":
            MessageLookupByLibrary.simpleMessage(
                "If you continue this will affect parents app and they can see all children under their number"),
        "Of": MessageLookupByLibrary.simpleMessage("Of"),
        "SignupAsa": MessageLookupByLibrary.simpleMessage("Sign up as a"),
        "SkipForNow": MessageLookupByLibrary.simpleMessage("Skip for now"),
        "absent": MessageLookupByLibrary.simpleMessage("absent"),
        "active": MessageLookupByLibrary.simpleMessage("Active"),
        "activeStudents":
            MessageLookupByLibrary.simpleMessage("Active Students"),
        "activities": MessageLookupByLibrary.simpleMessage("Activities"),
        "activitiesCompleted":
            MessageLookupByLibrary.simpleMessage("Activities completed"),
        "activity": MessageLookupByLibrary.simpleMessage("Activity"),
        "activityChart": MessageLookupByLibrary.simpleMessage("Activity chart"),
        "activityDescription":
            MessageLookupByLibrary.simpleMessage("Activity Description"),
        "activityName": MessageLookupByLibrary.simpleMessage("Activity Name"),
        "add": MessageLookupByLibrary.simpleMessage("Add"),
        "addANewStaffMember":
            MessageLookupByLibrary.simpleMessage("Add a new staff member"),
        "addActivity": MessageLookupByLibrary.simpleMessage("Add Activity"),
        "addAnnouncement":
            MessageLookupByLibrary.simpleMessage("Add Announcement"),
        "addImage": MessageLookupByLibrary.simpleMessage("Add Image"),
        "addMedia": MessageLookupByLibrary.simpleMessage("Add Media"),
        "addNewBill": MessageLookupByLibrary.simpleMessage("Add New Bill"),
        "addNewClass": MessageLookupByLibrary.simpleMessage("Add New Class"),
        "addNewEvent": MessageLookupByLibrary.simpleMessage("Add New Event"),
        "addNewInvoice": MessageLookupByLibrary.simpleMessage("Add New Income"),
        "addNewQuestion":
            MessageLookupByLibrary.simpleMessage("Add New Question"),
        "addNewSection":
            MessageLookupByLibrary.simpleMessage("Add New Section"),
        "addNewStudents":
            MessageLookupByLibrary.simpleMessage("Add New Students"),
        "addNote": MessageLookupByLibrary.simpleMessage("Add Note"),
        "addNurseryActivities":
            MessageLookupByLibrary.simpleMessage("Add New Activity "),
        "addNurseryTeam":
            MessageLookupByLibrary.simpleMessage("Add Nursery Team"),
        "addNurseryTeamMember":
            MessageLookupByLibrary.simpleMessage("Add New Team Member"),
        "addParentsPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Add Parents Phone number"),
        "addPickupPerson":
            MessageLookupByLibrary.simpleMessage("Add Pickup Person"),
        "addPlan": MessageLookupByLibrary.simpleMessage("Add Plan"),
        "addStudents": MessageLookupByLibrary.simpleMessage("Add Students"),
        "addedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Added successfully"),
        "address": MessageLookupByLibrary.simpleMessage("Address"),
        "adminAlreadyRegistered":
            MessageLookupByLibrary.simpleMessage("Admin already registered"),
        "adminSignUp":
            MessageLookupByLibrary.simpleMessage("Administrator Sign up"),
        "administrator": MessageLookupByLibrary.simpleMessage("Administrator"),
        "all": MessageLookupByLibrary.simpleMessage("All"),
        "allStudents": MessageLookupByLibrary.simpleMessage("All Students"),
        "alreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Already have an account?"),
        "amount": MessageLookupByLibrary.simpleMessage("Amount"),
        "announcements": MessageLookupByLibrary.simpleMessage("Announcements"),
        "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
        "areYouSureToDeleteThisActivity": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this activity ?"),
        "areYouSureToDeleteThisBill": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this bill ?"),
        "areYouSureToDeleteThisClass": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this class ?"),
        "areYouSureToDeleteThisEvent": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this event ?"),
        "areYouSureToDeleteThisInvoice": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this Income ?"),
        "areYouSureToDeleteThisQuestion": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this question ?"),
        "areYouSureToDeleteThisStudent": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this student ?"),
        "areYouSureToDeleteThisSupply": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this supply ?"),
        "areYouSureToDeleteThisTeacher": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete this teacher ?"),
        "areYouSureToDeleteYourAccount": MessageLookupByLibrary.simpleMessage(
            "Are you sure to delete your account ?"),
        "areYouSureToMakeThisSubscriptionPaid":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure to make this subscription paid ?"),
        "areYouSureToMakeThisSubscriptionPaidFor": m0,
        "areYouSureToSendSubscriptionRemind":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure to send subscription remind ?"),
        "areYouSureYouWantToDeleteThisAnnouncement":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure you want to delete this announcement?"),
        "areYouSureYouWantToDeleteThisPlan":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure you want to delete this plan?"),
        "assign": MessageLookupByLibrary.simpleMessage("Assign"),
        "assignActivityToClass":
            MessageLookupByLibrary.simpleMessage("Assign Activity To Class"),
        "assignSupplyToStudent":
            MessageLookupByLibrary.simpleMessage("Assign Supply To Student"),
        "assignToClass":
            MessageLookupByLibrary.simpleMessage("Assign to class"),
        "assigned": MessageLookupByLibrary.simpleMessage("Assigned"),
        "attendance": MessageLookupByLibrary.simpleMessage("Attendance"),
        "attendanceChart":
            MessageLookupByLibrary.simpleMessage("Attendance chart"),
        "attendanceTracking":
            MessageLookupByLibrary.simpleMessage("Attendance Tracking"),
        "attended": MessageLookupByLibrary.simpleMessage("attended"),
        "attendeesOfToday":
            MessageLookupByLibrary.simpleMessage("Attendees of Today"),
        "back": MessageLookupByLibrary.simpleMessage("Back"),
        "billAmount": MessageLookupByLibrary.simpleMessage("Bill Amount"),
        "billName": MessageLookupByLibrary.simpleMessage("Bill Name"),
        "bills": MessageLookupByLibrary.simpleMessage("Bills"),
        "billsChart": MessageLookupByLibrary.simpleMessage("Bills chart"),
        "birthDate": MessageLookupByLibrary.simpleMessage("Birth Date"),
        "breakfast": MessageLookupByLibrary.simpleMessage("Breakfast"),
        "by": MessageLookupByLibrary.simpleMessage("By"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "changeLanguage":
            MessageLookupByLibrary.simpleMessage("Change Language"),
        "changePassword":
            MessageLookupByLibrary.simpleMessage("Change Password"),
        "chooseActivityAssignType":
            MessageLookupByLibrary.simpleMessage("Choose activity assign type"),
        "classActivities":
            MessageLookupByLibrary.simpleMessage("Class Activities"),
        "classDescription":
            MessageLookupByLibrary.simpleMessage("Class Description"),
        "className": MessageLookupByLibrary.simpleMessage("Class Name"),
        "classes": MessageLookupByLibrary.simpleMessage("Classes"),
        "clear": MessageLookupByLibrary.simpleMessage("Clear"),
        "clickToContact":
            MessageLookupByLibrary.simpleMessage("Click to contact"),
        "completeVerification":
            MessageLookupByLibrary.simpleMessage("Complete Verification"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "confirmNewPassword":
            MessageLookupByLibrary.simpleMessage("Confirm new password"),
        "confirmation": MessageLookupByLibrary.simpleMessage("Confirmation"),
        "congratulations":
            MessageLookupByLibrary.simpleMessage("Congratulations"),
        "contactSupport":
            MessageLookupByLibrary.simpleMessage("Contact Support"),
        "createANewClass":
            MessageLookupByLibrary.simpleMessage("Create a new class"),
        "createANewSupply":
            MessageLookupByLibrary.simpleMessage("Add a new Supply"),
        "createNewClass":
            MessageLookupByLibrary.simpleMessage("Create new class"),
        "currentActivity":
            MessageLookupByLibrary.simpleMessage("Current Activity"),
        "currentMonth": MessageLookupByLibrary.simpleMessage("Current month"),
        "dailySchedule": MessageLookupByLibrary.simpleMessage("Daily Schedule"),
        "dashboard": MessageLookupByLibrary.simpleMessage("Dashboard"),
        "date": MessageLookupByLibrary.simpleMessage("Date"),
        "day": MessageLookupByLibrary.simpleMessage("Day"),
        "delete": MessageLookupByLibrary.simpleMessage("Delete"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete Account"),
        "deleteAnnouncement":
            MessageLookupByLibrary.simpleMessage("Delete Announcement"),
        "deletePlan": MessageLookupByLibrary.simpleMessage("Delete Plan"),
        "deletedSuccessfully":
            MessageLookupByLibrary.simpleMessage("deleted successfully"),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "didNotGetCode":
            MessageLookupByLibrary.simpleMessage("Didn’t get the code?"),
        "dontHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("You don’t have account?"),
        "due": MessageLookupByLibrary.simpleMessage("Due"),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "editActivity": MessageLookupByLibrary.simpleMessage("Edit Activity"),
        "editAnnouncement":
            MessageLookupByLibrary.simpleMessage("Edit Announcement"),
        "editClass": MessageLookupByLibrary.simpleMessage("Edit Class"),
        "editEvent": MessageLookupByLibrary.simpleMessage("Edit Event"),
        "editPlan": MessageLookupByLibrary.simpleMessage("Edit Plan"),
        "editSuccessfully":
            MessageLookupByLibrary.simpleMessage("Edit successfully"),
        "editTeacher": MessageLookupByLibrary.simpleMessage("Edit Teacher"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emergency": MessageLookupByLibrary.simpleMessage("Emergency"),
        "english": MessageLookupByLibrary.simpleMessage("English"),
        "enter": MessageLookupByLibrary.simpleMessage("Enter"),
        "enterNewPassword":
            MessageLookupByLibrary.simpleMessage("Enter new password"),
        "enterOtp": MessageLookupByLibrary.simpleMessage("Enter OTP"),
        "enterPhoneNumberFirst": MessageLookupByLibrary.simpleMessage(
            "First enter your phone number"),
        "enterPickupPerson":
            MessageLookupByLibrary.simpleMessage("Enter pickup person"),
        "enterSectionDescription":
            MessageLookupByLibrary.simpleMessage("Enter section description"),
        "enterSectionTitle":
            MessageLookupByLibrary.simpleMessage("Enter section title"),
        "enterValidNurseryName":
            MessageLookupByLibrary.simpleMessage("Enter valid nursery name"),
        "enterValidPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter valid phone number"),
        "errorOccurred": MessageLookupByLibrary.simpleMessage("Error occurred"),
        "eventName": MessageLookupByLibrary.simpleMessage("Event Name"),
        "eventThisMonth":
            MessageLookupByLibrary.simpleMessage("Event this month"),
        "eventType": MessageLookupByLibrary.simpleMessage("Event Type"),
        "events": MessageLookupByLibrary.simpleMessage("Events"),
        "exams": MessageLookupByLibrary.simpleMessage("Exams"),
        "father": MessageLookupByLibrary.simpleMessage("Father"),
        "fees": MessageLookupByLibrary.simpleMessage("Fees"),
        "financial": MessageLookupByLibrary.simpleMessage("Financial"),
        "finishLetsStart":
            MessageLookupByLibrary.simpleMessage("Finish, let’s start"),
        "food": MessageLookupByLibrary.simpleMessage("Food"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("Forget Password"),
        "friday": MessageLookupByLibrary.simpleMessage("Friday"),
        "from": MessageLookupByLibrary.simpleMessage("From"),
        "fromTimeShouldBeBeforeToTime": MessageLookupByLibrary.simpleMessage(
            "From time should be before to time"),
        "gender": MessageLookupByLibrary.simpleMessage("Gender"),
        "getTalkingFrom": MessageLookupByLibrary.simpleMessage(
            "Every day is a journey. \nSign in to join us."),
        "goodAfternoon":
            MessageLookupByLibrary.simpleMessage("Good Afternoon!"),
        "goodMorning": MessageLookupByLibrary.simpleMessage("Good Morning!"),
        "haveAnyQuestionsContactUs": MessageLookupByLibrary.simpleMessage(
            "Have any questions?\nContact us"),
        "history": MessageLookupByLibrary.simpleMessage("History"),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "homeAddress": MessageLookupByLibrary.simpleMessage("Home Address"),
        "iHaveReadThe": MessageLookupByLibrary.simpleMessage("I have read the"),
        "inClothes": MessageLookupByLibrary.simpleMessage("Clothes"),
        "inTheDiaper": MessageLookupByLibrary.simpleMessage("Diaper"),
        "inTheToilet": MessageLookupByLibrary.simpleMessage("Toilet"),
        "incomeChart": MessageLookupByLibrary.simpleMessage("Income chart"),
        "invoiceAmount": MessageLookupByLibrary.simpleMessage("Income Amount"),
        "invoiceName": MessageLookupByLibrary.simpleMessage("Income Name"),
        "invoices": MessageLookupByLibrary.simpleMessage("Income"),
        "invoicesChart": MessageLookupByLibrary.simpleMessage("Income chart"),
        "letsDoAGreatJob":
            MessageLookupByLibrary.simpleMessage("Let’s do a great job"),
        "letsStart": MessageLookupByLibrary.simpleMessage("Let’s Start"),
        "logout": MessageLookupByLibrary.simpleMessage("Logout"),
        "lunch": MessageLookupByLibrary.simpleMessage("Lunch"),
        "matherPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Mather Phone number"),
        "maxStudentsReachedPleaseContactSupport":
            MessageLookupByLibrary.simpleMessage(
                "Max students reached, please contact support !"),
        "maxUploadFileSizeIsOnly5MB": MessageLookupByLibrary.simpleMessage(
            "Max upload image size is only 5 MB"),
        "maxUploadFilesIsOnly4":
            MessageLookupByLibrary.simpleMessage("Max upload images is only 4"),
        "mealAmount": MessageLookupByLibrary.simpleMessage("meal Amount"),
        "mealType": MessageLookupByLibrary.simpleMessage("Meal Type"),
        "meals": MessageLookupByLibrary.simpleMessage("Meals"),
        "media": MessageLookupByLibrary.simpleMessage("Media"),
        "members": MessageLookupByLibrary.simpleMessage("Members"),
        "message": MessageLookupByLibrary.simpleMessage("Message"),
        "messageSentSuccessfully":
            MessageLookupByLibrary.simpleMessage("Message sent successfully"),
        "messages": MessageLookupByLibrary.simpleMessage("Messages"),
        "monday": MessageLookupByLibrary.simpleMessage("Monday"),
        "month": MessageLookupByLibrary.simpleMessage("Month"),
        "more": MessageLookupByLibrary.simpleMessage("More"),
        "mother": MessageLookupByLibrary.simpleMessage("Mother"),
        "motherOrParentAlreadyRegistered": MessageLookupByLibrary.simpleMessage(
            "Mother or Parent already registered"),
        "motherPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Mother Phone number"),
        "myClass": MessageLookupByLibrary.simpleMessage("My Class"),
        "myClasses": MessageLookupByLibrary.simpleMessage("My Classes"),
        "name": MessageLookupByLibrary.simpleMessage("Name"),
        "next": MessageLookupByLibrary.simpleMessage("Next"),
        "noActivities": MessageLookupByLibrary.simpleMessage("No Activities"),
        "noActivitiesFound":
            MessageLookupByLibrary.simpleMessage("No activities found"),
        "noBills": MessageLookupByLibrary.simpleMessage("No Bills"),
        "noClasses": MessageLookupByLibrary.simpleMessage("No Classes"),
        "noData": MessageLookupByLibrary.simpleMessage("No Data"),
        "noEvents": MessageLookupByLibrary.simpleMessage("No events"),
        "noHistoryForThisDate":
            MessageLookupByLibrary.simpleMessage("No history for this date"),
        "noInvoices": MessageLookupByLibrary.simpleMessage("No Income"),
        "noMedia": MessageLookupByLibrary.simpleMessage("No Media"),
        "noNotifications":
            MessageLookupByLibrary.simpleMessage("No Notifications"),
        "noPersons": MessageLookupByLibrary.simpleMessage("No persons"),
        "noPlansForThisMonth":
            MessageLookupByLibrary.simpleMessage("No plans for this month"),
        "noQuestions": MessageLookupByLibrary.simpleMessage("No questions"),
        "noResultsFound":
            MessageLookupByLibrary.simpleMessage("No results found"),
        "noStudents": MessageLookupByLibrary.simpleMessage("No Students"),
        "noSubscriptionDateSet":
            MessageLookupByLibrary.simpleMessage("No subscription date set"),
        "noSupplies": MessageLookupByLibrary.simpleMessage("No Supplies"),
        "noTeachers": MessageLookupByLibrary.simpleMessage("No Teachers"),
        "none": MessageLookupByLibrary.simpleMessage("None"),
        "note": MessageLookupByLibrary.simpleMessage("Note"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
        "numberOfStudents":
            MessageLookupByLibrary.simpleMessage("Number of Students"),
        "nurseryActivities":
            MessageLookupByLibrary.simpleMessage("Add a Nursery Activities"),
        "nurseryLogo": MessageLookupByLibrary.simpleMessage("Nursery Logo"),
        "nurseryName": MessageLookupByLibrary.simpleMessage("Nursery Name"),
        "paid": MessageLookupByLibrary.simpleMessage("Paid"),
        "paidSuccessfully":
            MessageLookupByLibrary.simpleMessage("Paid successfully"),
        "parentPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Parent Phone number"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "passwordConfirmation":
            MessageLookupByLibrary.simpleMessage("Password Confirmation"),
        "passwordsDoNotMatch":
            MessageLookupByLibrary.simpleMessage("Passwords do not match"),
        "passwordsShouldMatch":
            MessageLookupByLibrary.simpleMessage("Passwords should match"),
        "persons": MessageLookupByLibrary.simpleMessage("Persons"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
        "pickImage": MessageLookupByLibrary.simpleMessage("Pick Image"),
        "pickupPerson": MessageLookupByLibrary.simpleMessage("Pickup Person"),
        "pickups": MessageLookupByLibrary.simpleMessage("Pickups"),
        "plan": MessageLookupByLibrary.simpleMessage("Plan"),
        "plans": MessageLookupByLibrary.simpleMessage("Plans"),
        "pleaseAcceptTerms":
            MessageLookupByLibrary.simpleMessage("Please accept terms"),
        "pleaseAddAtLeastOneSection": MessageLookupByLibrary.simpleMessage(
            "Please add at least one section"),
        "pleaseAddTheStudentSubscriptionDateFirst":
            MessageLookupByLibrary.simpleMessage(
                "Please add the student subscription date first before making payment."),
        "pleaseEnterAValidFromToTime": MessageLookupByLibrary.simpleMessage(
            "Please enter a valid from & to time"),
        "pleasePickAnImage":
            MessageLookupByLibrary.simpleMessage("Please pick an image"),
        "pleaseVerifyPhone":
            MessageLookupByLibrary.simpleMessage("Please verify phone"),
        "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
        "profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "question": MessageLookupByLibrary.simpleMessage("Question"),
        "reminderSentSuccessfully":
            MessageLookupByLibrary.simpleMessage("Reminder sent successfully"),
        "removeImage": MessageLookupByLibrary.simpleMessage("Remove Image"),
        "removeSection": MessageLookupByLibrary.simpleMessage("Remove Section"),
        "reports": MessageLookupByLibrary.simpleMessage("Reports"),
        "resend": MessageLookupByLibrary.simpleMessage("Resend"),
        "resendCode": MessageLookupByLibrary.simpleMessage("Resend Code"),
        "resetPassword":
            MessageLookupByLibrary.simpleMessage("Reset your password"),
        "results": MessageLookupByLibrary.simpleMessage("Results"),
        "saturday": MessageLookupByLibrary.simpleMessage("Saturday"),
        "save": MessageLookupByLibrary.simpleMessage("Save"),
        "savedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Saved successfully"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "searchClasses": MessageLookupByLibrary.simpleMessage("Search Classes"),
        "searchQuestion":
            MessageLookupByLibrary.simpleMessage("Search Question"),
        "searchStudent": MessageLookupByLibrary.simpleMessage("Search Student"),
        "section": MessageLookupByLibrary.simpleMessage("Section"),
        "sectionDescription":
            MessageLookupByLibrary.simpleMessage("Section Description"),
        "sectionImage": MessageLookupByLibrary.simpleMessage("Section Image"),
        "sectionTitle": MessageLookupByLibrary.simpleMessage("Section Title"),
        "sections": MessageLookupByLibrary.simpleMessage("Sections"),
        "selectClass":
            MessageLookupByLibrary.simpleMessage("Please select a class"),
        "selectClasses": MessageLookupByLibrary.simpleMessage("Select Classes"),
        "selectPeriod": MessageLookupByLibrary.simpleMessage("Select period"),
        "selectTarget": MessageLookupByLibrary.simpleMessage("Select Target"),
        "send": MessageLookupByLibrary.simpleMessage("Send"),
        "sendANewMessage":
            MessageLookupByLibrary.simpleMessage("Send a new message"),
        "sendANewMessageTo": m1,
        "sendSupplies": MessageLookupByLibrary.simpleMessage("Send Supplies"),
        "sendSupplyToStudent":
            MessageLookupByLibrary.simpleMessage("Send Supply To Student"),
        "sentVerificationCode": MessageLookupByLibrary.simpleMessage(
            "We sent a verification code to"),
        "sessions": MessageLookupByLibrary.simpleMessage("Sessions"),
        "setupYourClasses":
            MessageLookupByLibrary.simpleMessage("Setup your classes"),
        "signIn": MessageLookupByLibrary.simpleMessage("Sign In"),
        "signUp": MessageLookupByLibrary.simpleMessage("Sign Up"),
        "singleActivity":
            MessageLookupByLibrary.simpleMessage("Single Activity"),
        "skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "skipForNow": MessageLookupByLibrary.simpleMessage("Skip for Now"),
        "sleep": MessageLookupByLibrary.simpleMessage("Sleep"),
        "snack": MessageLookupByLibrary.simpleMessage("Snack"),
        "some": MessageLookupByLibrary.simpleMessage("Some"),
        "speakWithConfidence":
            MessageLookupByLibrary.simpleMessage("Speak with confidence"),
        "staff": MessageLookupByLibrary.simpleMessage("Staff"),
        "stool": MessageLookupByLibrary.simpleMessage("Stool"),
        "student": MessageLookupByLibrary.simpleMessage("Student"),
        "studentAndClass":
            MessageLookupByLibrary.simpleMessage("Student & Class"),
        "studentName": MessageLookupByLibrary.simpleMessage("Student Name"),
        "students": MessageLookupByLibrary.simpleMessage("Students"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "subscriptionDate":
            MessageLookupByLibrary.simpleMessage("Subscription Date"),
        "subscriptionDateNeeded":
            MessageLookupByLibrary.simpleMessage("Subscription date needed"),
        "subscriptionDateRequired":
            MessageLookupByLibrary.simpleMessage("Subscription Date Required"),
        "subscriptionExpiredPleaseContactSupport":
            MessageLookupByLibrary.simpleMessage(
                "Subscription expired, please contact support !"),
        "subscriptionRemind":
            MessageLookupByLibrary.simpleMessage("Subscription Remind"),
        "subscriptions": MessageLookupByLibrary.simpleMessage("Subscriptions"),
        "sunday": MessageLookupByLibrary.simpleMessage("Sunday"),
        "supplies": MessageLookupByLibrary.simpleMessage("Supplies"),
        "supply": MessageLookupByLibrary.simpleMessage("Supply"),
        "supplyName": MessageLookupByLibrary.simpleMessage("supply Name"),
        "tClass": MessageLookupByLibrary.simpleMessage("Class"),
        "target": MessageLookupByLibrary.simpleMessage("Target"),
        "teacher": MessageLookupByLibrary.simpleMessage("Teacher"),
        "teacherInfo": MessageLookupByLibrary.simpleMessage("Teacher info"),
        "teacherName": MessageLookupByLibrary.simpleMessage("Teacher Name"),
        "teacherSignUp":
            MessageLookupByLibrary.simpleMessage("Teacher Sign up"),
        "teachers": MessageLookupByLibrary.simpleMessage("Teachers"),
        "team": MessageLookupByLibrary.simpleMessage("Team"),
        "thisFieldIsRequired":
            MessageLookupByLibrary.simpleMessage("This field is required"),
        "thursday": MessageLookupByLibrary.simpleMessage("Thursday"),
        "title": MessageLookupByLibrary.simpleMessage("Title"),
        "to": MessageLookupByLibrary.simpleMessage("To"),
        "today": MessageLookupByLibrary.simpleMessage("Today"),
        "toilet": MessageLookupByLibrary.simpleMessage("Toilet"),
        "toiletType": MessageLookupByLibrary.simpleMessage("Toilet Type"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "tuesday": MessageLookupByLibrary.simpleMessage("Tuesday"),
        "unAssign": MessageLookupByLibrary.simpleMessage("UnAssign"),
        "unableToCalculateNextPaymentDate":
            MessageLookupByLibrary.simpleMessage(
                "Unable to calculate next payment date."),
        "unpaid": MessageLookupByLibrary.simpleMessage("Unpaid"),
        "update": MessageLookupByLibrary.simpleMessage("Update"),
        "updateRequired": MessageLookupByLibrary.simpleMessage(
            "An update is required to continue using the app. Please update it now."),
        "uploadLogo": MessageLookupByLibrary.simpleMessage("Upload logo"),
        "urine": MessageLookupByLibrary.simpleMessage("Urine"),
        "userNotFound": MessageLookupByLibrary.simpleMessage("User not Found"),
        "validateYourPhoneFirstPlease": MessageLookupByLibrary.simpleMessage(
            "Validate your phone first please"),
        "verificationCodeIsWrong":
            MessageLookupByLibrary.simpleMessage("Verification code is wrong"),
        "verificationSuccessful":
            MessageLookupByLibrary.simpleMessage("Verification successful"),
        "verify": MessageLookupByLibrary.simpleMessage("verify"),
        "warning": MessageLookupByLibrary.simpleMessage("Warning"),
        "wednesday": MessageLookupByLibrary.simpleMessage("Wednesday"),
        "weekly": MessageLookupByLibrary.simpleMessage("Weekly"),
        "weeklyActivity":
            MessageLookupByLibrary.simpleMessage("Weekly Activity"),
        "youAreParentPleaseLoginOnParentApplication":
            MessageLookupByLibrary.simpleMessage(
                "You are parent, please login on parent application !"),
        "youAreParentPleaseRegisterOnParentApplication":
            MessageLookupByLibrary.simpleMessage(
                "You are parent, please register on parent application !"),
        "youCannotDeleteThisQuestionBecauseitsHasStudentResults":
            MessageLookupByLibrary.simpleMessage(
                "You cannot delete this question because it\'s has student results")
      };
}
