import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/subscriptions_tab_screen/widgets/subscription_top_section.widget.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/subscriptions_tab_screen/widgets/subscriptions_card_widget.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/subscriptions_tab_screen/widgets/subscriptions_tab_bar.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/widgets/students_grid_view.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class SubscriptionsTabScreen extends HookConsumerWidget {
  const SubscriptionsTabScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getSubscriptionsStudentsFuture =
        ref.watch(getSubscriptionsStudentsProvider(context));

    return getSubscriptionsStudentsFuture.get(
      data: (students) {
        return HookBuilder(builder: (context) {
          final isEmptyList = students.isEmpty;

          if (isEmptyList) {
            return const EmptyStudentsList(
              navigateWidget: SubscriptionsTabScreen(),
            );
          }

          final isPaid = useState<bool>(true);

          final paidStudents = useState(students
              .where((student) {
                // Check if student has subscription date
                if (student.subscriptionDate == null) return false;

                // Check if student has paid for current subscription period
                final nextPaymentDate = student.subscriptionDate!.nextPaymentDate;
                if (nextPaymentDate == null) return false;

                return student.subscriptions.firstWhereOrNull(
                  (element) {
                    final paymentDate = element.date.formatStringToDateTime;
                    return paymentDate.year == nextPaymentDate.year &&
                           paymentDate.month == nextPaymentDate.month &&
                           element.isPaid;
                  },
                ) != null;
              })
              .toList());

          final unpaidStudents = useState(students
              .where((student) {
                // If student has no subscription date, they need to set it first
                if (student.subscriptionDate == null) return true;

                // Check if student needs to pay for current subscription period
                final nextPaymentDate = student.subscriptionDate!.nextPaymentDate;
                if (nextPaymentDate == null) return true;

                return student.subscriptions.firstWhereOrNull(
                  (element) {
                    final paymentDate = element.date.formatStringToDateTime;
                    return paymentDate.year == nextPaymentDate.year &&
                           paymentDate.month == nextPaymentDate.month &&
                           element.isPaid;
                  },
                ) == null;
              })
              .toList());

          return StatefulBuilder(builder: (context, setState) {
            return Column(
              children: [
                SubscriptionsTabBarWidget(
                  isPaid: isPaid,
                ),
                context.largeGap,
                SubscriptionTopSectionWidget(
                  isPaid: isPaid.value,
                  paidStudentsList: paidStudents.value,
                  unpaidStudentsList: unpaidStudents.value,
                ),
                context.mediumGap,
                const Divider(
                  color: ColorManager.grey,
                  thickness: 1,
                ).paddingSymmetric(horizontal: 10),
                context.xSmallGap,
                Expanded(
                  child: BaseList(
                    data: isPaid.value
                        ? paidStudents.value
                        : unpaidStudents.value,
                    separatorGap: context.mediumGap,
                    itemBuilder: (data, index) {
                      return SubscriptionsCardWidget(
                        student: data,
                        isPaidTab: isPaid.value,
                        paidStudents: paidStudents,
                        unPaidStudents: unpaidStudents,
                        setState: setState,
                      );
                    },
                    mainAxisSpacing: 15.h,
                    crossAxisSpacing: 10,
                  ),
                ),
              ],
            );
          });
        });
      },
    );
  }
}
